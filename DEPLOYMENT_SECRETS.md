# FlexLiving Reviews Dashboard - Deployment Secrets Guide

## 🔐 Required Secrets for Production Deployment

This document outlines all the sensitive environment variables that need to be configured in Render.com for your FlexLiving Reviews Dashboard deployment.

### 🚨 IMPORTANT SECURITY NOTES
- **NEVER commit actual secrets to version control**
- Use <PERSON><PERSON>'s environment variable dashboard to set sensitive values
- Rotate secrets regularly for security
- Use different secrets for staging and production environments

---

## 📋 Backend Service Secrets

### 1. Database Configuration
```
MONGODB_URI
```
**Description**: MongoDB Atlas connection string  
**Format**: `mongodb+srv://username:<EMAIL>/flexliving-reviews?retryWrites=true&w=majority`  
**How to get**: Create MongoDB Atlas cluster and get connection string from Atlas dashboard  
**Required**: ✅ Yes

### 2. Hostaway API Configuration
```
HOSTAWAY_API_KEY
```
**Description**: API key for Hostaway integration (Account ID: 61148)  
**Format**: Long alphanumeric string  
**How to get**: From your Hostaway account dashboard → API settings  
**Required**: ✅ Yes (for Hostaway features)  
**Current Value**: `f94377ebbbb479490bb3ec364649168dc443dda2e4830facaf5de2e74ccc9152` (from your .env)

### 3. Google Places API Configuration
```
GOOGLE_PLACES_API_KEY
GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET
```
**Description**: Google Cloud API credentials for Places API and OAuth  
**How to get**: Google Cloud Console → APIs & Services → Credentials  
**Required**: ✅ Yes (for Google Reviews features)  
**Current Values**: Available in your backend/.env file

### 4. Security Configuration
```
JWT_SECRET
```
**Description**: Secret key for JWT token signing  
**Format**: Long random string (64+ characters recommended)  
**How to generate**: `node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"`  
**Required**: ✅ Yes  
**Current Value**: `flexliving-secret-key-2024` (should be changed for production)

---

## 🌐 Frontend Service Configuration

### 1. API Configuration
```
REACT_APP_API_URL
```
**Description**: Backend API URL  
**Format**: `https://your-backend-service.onrender.com/api`  
**How to set**: Render will auto-populate this from backend service URL  
**Required**: ✅ Yes

---

## 🗄️ Database Setup (MongoDB Atlas)

### Step 1: Create MongoDB Atlas Account
1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Create free account or sign in
3. Create new project: "FlexLiving Reviews"

### Step 2: Create Database Cluster
1. Click "Build a Database"
2. Choose "M0 Sandbox" (free tier) or paid tier for production
3. Select region closest to your Render services (Oregon recommended)
4. Name cluster: `flexliving-reviews-cluster`

### Step 3: Configure Database Access
1. Go to "Database Access" → "Add New Database User"
2. Username: `flexliving-app`
3. Password: Generate secure password
4. Database User Privileges: "Read and write to any database"

### Step 4: Configure Network Access
1. Go to "Network Access" → "Add IP Address"
2. Add `0.0.0.0/0` (allow access from anywhere) for Render deployment
3. Or add specific Render IP ranges if available

### Step 5: Get Connection String
1. Go to "Databases" → "Connect" → "Connect your application"
2. Copy connection string
3. Replace `<password>` with your database user password
4. Replace `<dbname>` with `flexliving-reviews`

---

## 🔧 Render.com Environment Variable Setup

### Backend Service Environment Variables
Set these in Render Dashboard → Your Backend Service → Environment:

| Variable Name | Value | Type |
|---------------|-------|------|
| `NODE_ENV` | `production` | Plain Text |
| `PORT` | `5000` | Plain Text |
| `MONGODB_URI` | `mongodb+srv://...` | Secret |
| `HOSTAWAY_ACCOUNT_ID` | `61148` | Plain Text |
| `HOSTAWAY_API_KEY` | `your_api_key` | Secret |
| `HOSTAWAY_API_URL` | `https://api.hostaway.com/v1` | Plain Text |
| `GOOGLE_PLACES_API_KEY` | `your_google_key` | Secret |
| `GOOGLE_PLACES_BASE_URL` | `https://places.googleapis.com/v1` | Plain Text |
| `GOOGLE_CLIENT_ID` | `your_client_id` | Secret |
| `GOOGLE_CLIENT_SECRET` | `your_client_secret` | Secret |
| `JWT_SECRET` | `your_jwt_secret` | Secret |
| `CORS_ORIGIN` | Auto-populated by Render | Auto |

### Frontend Service Environment Variables
Set these in Render Dashboard → Your Frontend Service → Environment:

| Variable Name | Value | Type |
|---------------|-------|------|
| `REACT_APP_API_URL` | Auto-populated by Render | Auto |
| `GENERATE_SOURCEMAP` | `false` | Plain Text |

---

## 🔄 Secret Rotation Schedule

### Recommended Rotation Frequency:
- **JWT_SECRET**: Every 90 days
- **API Keys**: Every 180 days or when compromised
- **Database passwords**: Every 180 days
- **OAuth secrets**: Every 365 days or when compromised

### Rotation Process:
1. Generate new secret
2. Update in Render environment variables
3. Redeploy services
4. Verify functionality
5. Revoke old secret

---

## 🚨 Security Checklist

- [ ] All secrets are stored in Render environment variables (not in code)
- [ ] Database user has minimal required permissions
- [ ] Network access is restricted to necessary IPs
- [ ] JWT secret is cryptographically secure (64+ characters)
- [ ] API keys are from official provider dashboards
- [ ] Secrets are different between staging and production
- [ ] Regular secret rotation schedule is established
