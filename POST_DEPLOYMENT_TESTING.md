# Post-Deployment Testing Guide

This guide provides comprehensive testing procedures to verify your FlexLiving Reviews Dashboard deployment is working correctly.

## 🧪 Testing Checklist

### ✅ Backend API Testing

#### 1. Health Check Endpoint
```bash
# Test backend health
curl -X GET https://your-backend-service.onrender.com/api/health

# Expected Response:
{
  "status": "ok",
  "message": "FlexLiving Reviews API",
  "timestamp": "2024-01-XX...",
  "database": {
    "status": "healthy",
    "state": "connected"
  },
  "uptime": 123.45
}
```

#### 2. API Documentation
```bash
# Access Swagger UI
curl -I https://your-backend-service.onrender.com/api-docs

# Expected: HTTP 200 OK
# Browser test: Visit the URL to see interactive API docs
```

#### 3. Reviews API
```bash
# Get all reviews
curl -X GET https://your-backend-service.onrender.com/api/reviews

# Get review statistics
curl -X GET https://your-backend-service.onrender.com/api/reviews/statistics

# Get Hostaway reviews (required endpoint)
curl -X GET https://your-backend-service.onrender.com/api/reviews/hostaway
```

#### 4. Properties API
```bash
# Get all properties
curl -X GET https://your-backend-service.onrender.com/api/properties

# Get specific property
curl -X GET https://your-backend-service.onrender.com/api/properties/prop_001
```

#### 5. Hostaway Integration
```bash
# Test Hostaway API connection
curl -X GET https://your-backend-service.onrender.com/api/hostaway/reviews

# Test sync functionality
curl -X POST https://your-backend-service.onrender.com/api/hostaway/sync
```

#### 6. Google Places Integration
```bash
# Test Google API status
curl -X GET https://your-backend-service.onrender.com/api/google/status

# Test connection
curl -X GET https://your-backend-service.onrender.com/api/google/test-connection
```

### ✅ Frontend Testing

#### 1. Application Load
- [ ] Visit: `https://your-frontend-service.onrender.com`
- [ ] Verify dashboard loads without errors
- [ ] Check browser console for JavaScript errors
- [ ] Verify FlexLiving branding and styling

#### 2. Navigation Testing
- [ ] Test navigation between Dashboard and Property Detail pages
- [ ] Verify React Router works (no 404 errors on refresh)
- [ ] Test browser back/forward buttons

#### 3. Dashboard Functionality
- [ ] **Reviews List**: Verify reviews load and display correctly
- [ ] **Filters**: Test status, source, and date filters
- [ ] **Statistics**: Check review statistics display
- [ ] **Property Selection**: Test property dropdown/selection
- [ ] **Refresh Button**: Test data refresh functionality

#### 4. API Integration Testing
- [ ] **Data Loading**: Verify data loads from backend API
- [ ] **Error Handling**: Test behavior when backend is unavailable
- [ ] **Loading States**: Check loading indicators work
- [ ] **Real-time Updates**: Test data refresh functionality

#### 5. Hostaway Features
- [ ] **Sync Button**: Test Hostaway data synchronization
- [ ] **Review Display**: Verify Hostaway reviews display correctly
- [ ] **Status Updates**: Test review status changes
- [ ] **Error Messages**: Verify error handling for API failures

#### 6. Google Reviews Features
- [ ] **Google Integration**: Test Google Places API integration
- [ ] **Review Fetching**: Verify Google reviews are fetched
- [ ] **Display**: Check Google reviews display correctly
- [ ] **Error Handling**: Test API error scenarios

### ✅ Database Testing

#### 1. MongoDB Atlas Connection
```bash
# Test from backend service shell
npm run setup-db

# Expected output:
# ✅ Connected to MongoDB successfully
# ✅ All indexes created successfully
# ✅ Models validated successfully
```

#### 2. Data Persistence
- [ ] Create a test review through the API
- [ ] Verify it appears in the dashboard
- [ ] Check it exists in MongoDB Atlas dashboard
- [ ] Test data survives service restarts

#### 3. Performance Testing
```bash
# Test database query performance
curl -w "@curl-format.txt" -o /dev/null -s \
  https://your-backend-service.onrender.com/api/reviews
```

### ✅ Security Testing

#### 1. CORS Configuration
```bash
# Test CORS from different origin
curl -H "Origin: https://malicious-site.com" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: X-Requested-With" \
  -X OPTIONS \
  https://your-backend-service.onrender.com/api/health
```

#### 2. Security Headers
```bash
# Check security headers
curl -I https://your-backend-service.onrender.com/api/health

# Should include:
# X-Content-Type-Options: nosniff
# X-Frame-Options: DENY
# X-XSS-Protection: 1; mode=block
```

#### 3. Environment Variables
- [ ] Verify sensitive data is not exposed in client-side code
- [ ] Check API keys are properly secured
- [ ] Confirm database credentials are not logged

### ✅ Performance Testing

#### 1. Load Testing
```bash
# Simple load test (10 concurrent requests)
for i in {1..10}; do
  curl -w "%{time_total}\n" -o /dev/null -s \
    https://your-backend-service.onrender.com/api/health &
done
wait

# Expected: All responses < 2 seconds
```

#### 2. Frontend Performance
- [ ] **Page Load Time**: < 3 seconds for initial load
- [ ] **API Response Time**: < 1 second for data fetching
- [ ] **Bundle Size**: Check with browser dev tools
- [ ] **Lighthouse Score**: Run Google Lighthouse audit

#### 3. Database Performance
- [ ] **Query Response Time**: < 500ms for typical queries
- [ ] **Connection Pool**: Monitor connection usage
- [ ] **Index Usage**: Verify queries use indexes efficiently

### ✅ Integration Testing

#### 1. End-to-End User Flows
- [ ] **Manager Workflow**: Login → View Reviews → Filter → Update Status
- [ ] **Public View**: Access public review page for a property
- [ ] **Data Sync**: Trigger Hostaway sync → Verify new data appears
- [ ] **Error Recovery**: Test app behavior during API outages

#### 2. Cross-Browser Testing
- [ ] **Chrome**: Test all functionality
- [ ] **Firefox**: Verify compatibility
- [ ] **Safari**: Check iOS/macOS compatibility
- [ ] **Edge**: Test Windows compatibility

#### 3. Mobile Responsiveness
- [ ] **Mobile Layout**: Test on various screen sizes
- [ ] **Touch Interactions**: Verify mobile-friendly interactions
- [ ] **Performance**: Check mobile performance

### ✅ Monitoring and Alerting

#### 1. Service Health Monitoring
- [ ] Set up Render service monitoring
- [ ] Configure uptime alerts
- [ ] Test alert notifications

#### 2. Database Monitoring
- [ ] Monitor MongoDB Atlas metrics
- [ ] Set up storage and performance alerts
- [ ] Test connection monitoring

#### 3. API Monitoring
- [ ] Monitor Hostaway API usage and limits
- [ ] Track Google Places API quota
- [ ] Set up error rate alerts

## 🚨 Troubleshooting Common Issues

### Backend Issues

#### Service Won't Start
```bash
# Check logs
render logs --service=flexliving-reviews-backend --tail

# Common fixes:
# 1. Verify all environment variables are set
# 2. Check MongoDB connection string
# 3. Ensure build completed successfully
```

#### Database Connection Errors
```bash
# Test connection manually
node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('✅ Connected'))
  .catch(err => console.error('❌ Failed:', err));
"
```

### Frontend Issues

#### API Connection Errors
- Check `REACT_APP_API_URL` environment variable
- Verify CORS configuration on backend
- Test API endpoints directly with curl

#### Build Failures
```bash
# Check build logs
render logs --service=flexliving-reviews-frontend

# Common fixes:
# 1. Clear npm cache: npm cache clean --force
# 2. Delete node_modules and reinstall
# 3. Check for TypeScript errors
```

### Performance Issues

#### Slow Response Times
- Check database query performance
- Monitor Render service metrics
- Consider upgrading service plan

#### High Memory Usage
- Monitor service resource usage
- Check for memory leaks in application
- Consider optimizing database queries

## 📊 Success Criteria

### Functional Requirements
- [ ] All API endpoints respond correctly
- [ ] Frontend loads and displays data
- [ ] Hostaway integration works (Account ID: 61148)
- [ ] Google Places API integration functions
- [ ] Database operations complete successfully

### Performance Requirements
- [ ] API response times < 1 second
- [ ] Frontend load time < 3 seconds
- [ ] Database queries < 500ms
- [ ] 99%+ uptime

### Security Requirements
- [ ] HTTPS enabled for all services
- [ ] Sensitive data properly secured
- [ ] CORS configured correctly
- [ ] Security headers present

## 🎉 Deployment Verification Complete

Once all tests pass, your FlexLiving Reviews Dashboard is successfully deployed and ready for production use!

### Next Steps
1. Set up monitoring and alerting
2. Configure backup procedures
3. Plan for scaling as usage grows
4. Document operational procedures
5. Train users on the new system
