# FlexLiving Reviews Backend - Production Environment Variables Template
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=5000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MongoDB Atlas connection string (replace with your actual Atlas cluster)
# Format: mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
MONGODB_URI=mongodb+srv://username:<EMAIL>/flexliving-reviews?retryWrites=true&w=majority

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Frontend URL (will be automatically set by Render for the frontend service)
CORS_ORIGIN=https://your-frontend-app.onrender.com

# =============================================================================
# HOSTAWAY API CONFIGURATION
# =============================================================================
HOSTAWAY_ACCOUNT_ID=61148
HOSTAWAY_API_URL=https://api.hostaway.com/v1
# SENSITIVE: Get this from your Hostaway account dashboard
HOSTAWAY_API_KEY=your_hostaway_api_key_here

# =============================================================================
# GOOGLE PLACES API CONFIGURATION
# =============================================================================
GOOGLE_PLACES_BASE_URL=https://places.googleapis.com/v1
# SENSITIVE: Get this from Google Cloud Console
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
# SENSITIVE: OAuth credentials for Google API
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# =============================================================================
# SECURITY
# =============================================================================
# SENSITIVE: Use a strong, randomly generated secret for JWT tokens
# Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=your_jwt_secret_here

# =============================================================================
# OPTIONAL: LOGGING AND MONITORING
# =============================================================================
# LOG_LEVEL=info
# SENTRY_DSN=your_sentry_dsn_here
