{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.12.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.1", "swagger-ui-express": "^5.0.1", "yamljs": "^0.3.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.5.2", "@types/swagger-ui-express": "^4.1.8", "@types/yamljs": "^0.2.34", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}