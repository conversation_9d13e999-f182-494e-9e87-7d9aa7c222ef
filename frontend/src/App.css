.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-nav {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-bg-secondary);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  text-decoration: none;
}

.logo-image {
  height: 32px;
  width: auto;
  object-fit: contain;
}

.logo-accent {
  color: var(--color-text-secondary);
  font-weight: 400;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.nav-link:hover {
  color: var(--color-accent-hover);
}

.app-main {
  flex: 1;
  background: var(--color-bg-primary);
}

@media (max-width: 768px) {
  .nav-links {
    gap: 1rem;
  }
  
  .nav-link {
    font-size: 0.875rem;
  }
}