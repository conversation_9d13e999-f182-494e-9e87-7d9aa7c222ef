.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: 1rem;
}

.reviews-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.select-all-btn {
  display: flex;
  align-items: center;
  padding: 0.25rem;
  background: transparent;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.select-all-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-primary);
}

.selected-count {
  color: var(--color-primary);
  font-weight: 500;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-controls span {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-right: 0.5rem;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.sort-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.sort-btn.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.sort-btn svg {
  opacity: 0.7;
}

/* Bulk Actions */
.bulk-actions {
  background: var(--color-primary);
  color: white;
  border-radius: var(--radius-lg);
  margin-bottom: 1rem;
  animation: slideDown 0.3s ease-out;
}

.bulk-actions-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
}

.bulk-actions-label {
  font-weight: 500;
}

.bulk-actions-buttons {
  display: flex;
  gap: 0.5rem;
}

.bulk-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.bulk-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.bulk-btn-primary:hover {
  background: rgba(59, 130, 246, 0.8);
}

.bulk-btn-secondary:hover {
  background: rgba(107, 114, 128, 0.8);
}

.bulk-btn-success:hover {
  background: rgba(34, 197, 94, 0.8);
}

.bulk-btn-danger:hover {
  background: rgba(239, 68, 68, 0.8);
}

.reviews-loading,
.reviews-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  text-align: center;
}

.reviews-loading p,
.reviews-empty p {
  color: var(--color-text-secondary);
  margin-top: 1rem;
}

.review-checkbox {
  display: flex;
  align-items: center;
  padding: 0.25rem;
  background: transparent;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.review-checkbox:hover {
  background: var(--color-bg-secondary);
  color: var(--color-primary);
}

.review-card {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-normal);
  animation: fadeIn 0.3s ease-out;
}

.review-card:hover {
  box-shadow: var(--shadow-md);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-bg-secondary);
}

.review-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.review-guest,
.review-property,
.review-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.review-guest .guest-name {
  color: var(--color-text-primary);
  font-weight: 500;
}

.review-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-icon {
  padding: 0.375rem;
  background: var(--color-bg-secondary);
  border: none;
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-icon:hover {
  background: var(--color-accent);
  color: var(--color-primary);
}

.btn-icon.active {
  background: var(--color-accent);
  color: var(--color-primary);
}

.review-content {
  position: relative;
}

.review-rating {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  margin-bottom: 1rem;
  font-weight: 600;
}

.review-rating.rating-excellent {
  background: #dcfce7;
  color: #166534;
}

.review-rating.rating-good {
  background: #d4f872;
  color: var(--color-primary);
}

.review-rating.rating-average {
  background: #fef3c7;
  color: #92400e;
}

.review-rating.rating-poor {
  background: #fee2e2;
  color: #991b1b;
}

.rating-value {
  font-size: 1rem;
}

.review-text {
  color: var(--color-text-primary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.private-review {
  background: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: var(--radius-md);
  margin: 1rem 0;
}

.private-label {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.private-text {
  color: var(--color-text-primary);
  font-style: italic;
}

.review-categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.category-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.category-name {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  text-transform: capitalize;
}

.category-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rating-bar {
  flex: 1;
  height: 6px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.rating-fill {
  height: 100%;
  background: var(--color-accent);
  transition: width var(--transition-normal);
}

.rating-text {
  font-size: 0.75rem;
  color: var(--color-text-primary);
  font-weight: 500;
  min-width: 35px;
}

.review-expanded {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-bg-secondary);
  animation: fadeIn 0.3s ease-out;
}

.response-section {
  margin-bottom: 1.5rem;
}

.response-section h4 {
  color: var(--color-text-primary);
  margin-bottom: 0.75rem;
}

.existing-response {
  background: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: var(--radius-md);
}

.existing-response p {
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.existing-response small {
  color: var(--color-text-secondary);
  font-size: 0.75rem;
}

.response-form {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.response-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--color-text-muted);
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: 0.875rem;
  resize: vertical;
  transition: border-color var(--transition-fast);
}

.response-form textarea:focus {
  outline: none;
  border-color: var(--color-accent);
}

.response-form button {
  align-self: flex-start;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.review-controls {
  margin-bottom: 1rem;
}

.review-controls label {
  display: block;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
}

.status-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
}

.review-expand-btn {
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  border: 1px solid var(--color-bg-secondary);
  border-radius: var(--radius-md);
  color: var(--color-text-primary);
  font-weight: 500;
  cursor: pointer;
  margin-top: 1rem;
  transition: all var(--transition-fast);
}

.review-expand-btn:hover {
  background: var(--color-bg-secondary);
  border-color: var(--color-text-muted);
}

@media (max-width: 768px) {
  .review-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .review-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .review-categories {
    grid-template-columns: 1fr;
  }
  
  .status-buttons {
    flex-direction: column;
  }
  
  .status-buttons button {
    width: 100%;
  }
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pagination Styles */
.pagination-info {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: 400;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1rem;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-top: 1rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--color-white);
  border: 1px solid var(--color-text-muted);
  border-radius: var(--radius-md);
  color: var(--color-text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--color-bg-secondary);
  border-color: var(--color-text-secondary);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-white);
  border: 1px solid var(--color-text-muted);
  border-radius: var(--radius-md);
  color: var(--color-text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.pagination-page:hover {
  background: var(--color-bg-secondary);
  border-color: var(--color-text-secondary);
}

.pagination-page.active {
  background: var(--color-accent);
  border-color: var(--color-accent);
  color: var(--color-primary);
  font-weight: 600;
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

/* Responsive Pagination */
@media (max-width: 768px) {
  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .pagination-pages {
    order: -1;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .pagination-page {
    width: 36px;
    height: 36px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .pagination-pages {
    flex-wrap: wrap;
    justify-content: center;
  }

  .pagination-info {
    text-align: center;
  }
}