.analytics {
  animation: fadeIn 0.3s ease-out;
}

.analytics-empty {
  padding: 3rem;
  text-align: center;
  color: var(--color-text-secondary);
  background: var(--color-white);
  border-radius: var(--radius-lg);
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics-card {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.analytics-card.small {
  grid-column: span 1;
}

.analytics-card h3 {
  font-size: 1.125rem;
  color: var(--color-primary);
  margin-bottom: 1.5rem;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
}

.metric-label {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary);
}

.insights-section {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.insights-section h3 {
  font-size: 1.125rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
}

.insights-grid {
  display: grid;
  gap: 0.75rem;
}

.insight-card {
  padding: 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.insight-card.warning {
  background: #fef3c7;
  color: #92400e;
}

.insight-card.success {
  background: #dcfce7;
  color: #166534;
}

@media (max-width: 1024px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .analytics-card.small {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .analytics-card {
    padding: 1rem;
  }
}