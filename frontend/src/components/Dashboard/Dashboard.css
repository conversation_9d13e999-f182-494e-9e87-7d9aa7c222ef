.dashboard {
  min-height: 100vh;
  background-color: var(--color-bg-primary);
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
}

.dashboard-loading p {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.dashboard-header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-bg-secondary);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.dashboard-header .header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.dashboard-header h1 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.dashboard-header .subtitle {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  max-width: 1280px;
  margin: 1rem auto 0;
  padding: 0 1rem;
}

.header-actions button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem 2rem;
}

.content-header {
  margin-bottom: 1.5rem;
}

.tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 2px solid var(--color-bg-secondary);
}

.tab {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: all var(--transition-fast);
}

.tab:hover {
  color: var(--color-text-primary);
}

.tab.active {
  color: var(--color-primary);
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-accent);
}

.reviews-section {
  animation: fadeIn 0.3s ease-out;
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
  }
  
  .header-actions button {
    width: 100%;
    justify-content: center;
  }
  
  .tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .tab {
    white-space: nowrap;
    font-size: 0.875rem;
  }
}