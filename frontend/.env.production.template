# FlexLiving Reviews Frontend - Production Environment Variables Template
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Backend API URL (will be automatically set by Render for the backend service)
REACT_APP_API_URL=https://your-backend-app.onrender.com/api

# =============================================================================
# BUILD CONFIGURATION
# =============================================================================
# Generate source maps for production debugging (optional)
GENERATE_SOURCEMAP=false

# =============================================================================
# OPTIONAL: ANALYTICS AND MONITORING
# =============================================================================
# REACT_APP_GOOGLE_ANALYTICS_ID=your_ga_id_here
# REACT_APP_SENTRY_DSN=your_sentry_dsn_here

# =============================================================================
# OPTIONAL: FEATURE FLAGS
# =============================================================================
# REACT_APP_ENABLE_GOOGLE_REVIEWS=true
# REACT_APP_ENABLE_HOSTAWAY_SYNC=true
