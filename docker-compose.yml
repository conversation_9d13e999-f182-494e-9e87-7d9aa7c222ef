version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: flexliving-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: flexliving-reviews
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./backend/src/utils/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - flexliving-network

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: flexliving-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: **************************************************************************
      CORS_ORIGIN: http://localhost:3000
      # Add your API keys here or use .env file
      HOSTAWAY_ACCOUNT_ID: ${HOSTAWAY_ACCOUNT_ID}
      HOSTAWAY_API_KEY: ${HOSTAWAY_API_KEY}
      HOSTAWAY_API_URL: ${HOSTAWAY_API_URL}
      GOOGLE_PLACES_API_KEY: ${GOOGLE_PLACES_API_KEY}
      GOOGLE_PLACES_BASE_URL: ${GOOGLE_PLACES_BASE_URL}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
    networks:
      - flexliving-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: flexliving-frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - flexliving-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
    driver: local

networks:
  flexliving-network:
    driver: bridge
